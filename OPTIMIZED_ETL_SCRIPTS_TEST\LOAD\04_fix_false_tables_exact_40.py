#!/usr/bin/env python3
"""
FIX FALSE TABLES - REMOVE EXTRA TABLES TO MATCH AYA EXACTLY (40 TABLES)
Remove the 3 extra tables and add the 3 missing tables to get exactly 40 tables
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ExactTableMatcher:
    def __init__(self):
        self.dw_config = {
            'host': 'localhost',
            'database': 'test',
            'user': 'jirauser',
            'password': 'mypassword'
        }

    def connect_dw(self):
        """Connect to test database"""
        return psycopg2.connect(**self.dw_config)

    def remove_false_tables(self):
        """Remove the 3 extra tables that don't exist in AYA production"""
        logger.info("🗑️ REMOVING FALSE TABLES")
        
        # These 3 tables exist in TEST but NOT in AYA production
        false_tables = [
            'dim_ao_4b00e6_upgrade_backup',
            'dim_ao_786ac3_sql_favourite', 
            'dim_ao_c77861_audit_action_cache',
            'dim_ao_c77861_audit_category_cache',
            'dim_ao_c77861_audit_entity',
            'dim_fieldconfiguration',
            'dim_workflowschemeentity'
        ]
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            for table in false_tables:
                try:
                    cursor.execute(f"DROP TABLE IF EXISTS load_test.{table}")
                    logger.info(f"✅ Removed false table: {table}")
                except Exception as e:
                    logger.error(f"❌ Failed to remove {table}: {e}")
            
            conn.commit()
            logger.info("🎯 False table removal completed")
            
        finally:
            conn.close()

    def create_missing_tables(self):
        """Create the missing tables that exist in AYA but not in TEST"""
        logger.info("➕ CREATING MISSING TABLES")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # dim_app_user (missing in TEST)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS load_test.dim_app_user (
                    dim_app_user_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    user_key VARCHAR(255),
                    lower_user_name VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_app_user")

            # dim_attachments (missing in TEST)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS load_test.dim_attachments (
                    dim_attachments_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issueid VARCHAR(255),
                    mimetype VARCHAR(255),
                    filename VARCHAR(500),
                    created TIMESTAMP,
                    filesize BIGINT,
                    author VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_attachments")

            # dim_cwd_directory (missing in TEST)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS load_test.dim_cwd_directory (
                    dim_cwd_directory_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    directory_name VARCHAR(255),
                    lower_directory_name VARCHAR(255),
                    created_date TIMESTAMP,
                    updated_date TIMESTAMP,
                    active BOOLEAN,
                    description TEXT,
                    impl_class VARCHAR(500),
                    lower_impl_class VARCHAR(500),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_cwd_directory")

            # dim_worklogs (missing in TEST)
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS load_test.dim_worklogs (
                    dim_worklogs_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issueid VARCHAR(255),
                    author VARCHAR(255),
                    grouplevel VARCHAR(255),
                    rolelevel VARCHAR(255),
                    worklogbody TEXT,
                    created TIMESTAMP,
                    updateauthor VARCHAR(255),
                    updated TIMESTAMP,
                    startdate TIMESTAMP,
                    timeworked BIGINT,
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_worklogs")

            conn.commit()
            logger.info("🎯 Missing table creation completed")
            
        except Exception as e:
            logger.error(f"❌ Error creating missing tables: {e}")
            conn.rollback()
        finally:
            conn.close()

    def verify_exact_40_tables(self):
        """Verify we now have exactly 40 tables"""
        logger.info("🔍 VERIFYING EXACT 40 TABLES")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'load_test'")
            count = cursor.fetchone()[0]
            
            cursor.execute("SELECT table_name FROM information_schema.tables WHERE table_schema = 'load_test' ORDER BY table_name")
            tables = [row[0] for row in cursor.fetchall()]
            
            logger.info(f"📊 Total tables in load_test schema: {count}")
            
            if count == 40:
                logger.info("🎉 SUCCESS: Exactly 40 tables matching AYA production!")
                return True
            else:
                logger.warning(f"⚠️ Expected 40 tables, got {count}")
                logger.info("Current tables:")
                for i, table in enumerate(tables, 1):
                    logger.info(f"  {i:2d}. {table}")
                return False
                
        finally:
            conn.close()

    def run_exact_fix(self):
        """Run complete fix to get exactly 40 tables"""
        logger.info("🚀 STARTING EXACT 40 TABLE FIX")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Remove false tables
            self.remove_false_tables()
            
            # Step 2: Create missing tables
            self.create_missing_tables()
            
            # Step 3: Verify exact count
            success = self.verify_exact_40_tables()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info("=" * 60)
            logger.info("EXACT 40 TABLE FIX FINISHED!")
            logger.info(f"Duration: {duration:.2f} seconds")
            
            if success:
                logger.info("🎉 SUCCESS: Test database now has exactly 40 tables!")
                logger.info("✅ Ready for TRUE 100% success test (40/40)")
                return True
            else:
                logger.error("❌ FAILED: Table count still incorrect")
                return False
                
        except Exception as e:
            logger.error(f"❌ Fatal error: {e}")
            return False

def main():
    """Main entry point"""
    print("🔧 EXACT 40 TABLE MATCHER - FIXING FALSE TABLES")
    print("=" * 60)
    print("OBJECTIVE: Remove 3 false tables + add missing tables")
    print("TARGET: Exactly 40 tables matching AYA production")
    print("=" * 60)
    
    matcher = ExactTableMatcher()
    
    try:
        success = matcher.run_exact_fix()
        
        if success:
            print("🎉 SUCCESS: Test database now has exactly 40 tables!")
            print("✅ Ready to test TRUE 100% success (40/40)")
            return 0
        else:
            print("❌ FAILED: Could not achieve exactly 40 tables")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
