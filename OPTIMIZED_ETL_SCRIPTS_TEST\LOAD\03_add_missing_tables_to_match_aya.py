#!/usr/bin/env python3
"""
ADD MISSING TABLES TO MATCH AYA PRODUCTION DATABASE
Creates the 15 missing dimension tables and renames 5 existing tables
to match the exact structure of AYA production database (40 tables total)
"""

import psycopg2
import logging
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AYATableMatcher:
    def __init__(self):
        self.dw_config = {
            'host': 'localhost',
            'database': 'test',
            'user': 'jirauser',
            'password': 'mypassword'
        }

    def connect_dw(self):
        """Connect to test database"""
        return psycopg2.connect(**self.dw_config)

    def rename_existing_tables(self):
        """Rename 5 existing tables to match AYA naming convention"""
        logger.info("🔄 RENAMING EXISTING TABLES TO MATCH AYA")
        
        renames = [
            ('dim_component', 'dim_components'),
            ('dim_customfield', 'dim_customfields'), 
            ('dim_jiraworkflows', 'dim_workflows'),
            ('dim_workflowscheme', 'dim_workflow_schemes'),
            ('dim_fieldconfigscheme', 'dim_field_configs')
        ]
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            for old_name, new_name in renames:
                try:
                    cursor.execute(f"ALTER TABLE load_test.{old_name} RENAME TO {new_name}")
                    logger.info(f"✅ Renamed {old_name} → {new_name}")
                except Exception as e:
                    logger.error(f"❌ Failed to rename {old_name}: {e}")
            
            conn.commit()
            logger.info("🎯 Table renaming completed")
            
        finally:
            conn.close()

    def create_missing_tables(self):
        """Create the 15 missing dimension tables"""
        logger.info("➕ CREATING 15 MISSING DIMENSION TABLES")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # dim_app_user
            cursor.execute("""
                CREATE TABLE load_test.dim_app_user (
                    dim_app_user_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    user_key VARCHAR(255),
                    lower_user_name VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_app_user")

            # dim_attachments
            cursor.execute("""
                CREATE TABLE load_test.dim_attachments (
                    dim_attachments_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issueid VARCHAR(255),
                    mimetype VARCHAR(255),
                    filename VARCHAR(500),
                    created TIMESTAMP,
                    filesize BIGINT,
                    author VARCHAR(255),
                    author_username VARCHAR(255),
                    author_email VARCHAR(255),
                    author_display_name VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_attachments")

            # dim_changegroups
            cursor.execute("""
                CREATE TABLE load_test.dim_changegroups (
                    dim_changegroups_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issueid VARCHAR(255),
                    author VARCHAR(255),
                    created TIMESTAMP,
                    author_username VARCHAR(255),
                    author_email VARCHAR(255),
                    author_display_name VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_changegroups")

            # dim_changeitem
            cursor.execute("""
                CREATE TABLE load_test.dim_changeitem (
                    dim_changeitem_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    groupid VARCHAR(255),
                    fieldtype VARCHAR(255),
                    field VARCHAR(255),
                    oldvalue TEXT,
                    oldstring TEXT,
                    newvalue TEXT,
                    newstring TEXT,
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_changeitem")

            # dim_customfieldvalue
            cursor.execute("""
                CREATE TABLE load_test.dim_customfieldvalue (
                    dim_customfieldvalue_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issue VARCHAR(255),
                    customfield VARCHAR(255),
                    parentkey VARCHAR(255),
                    stringvalue TEXT,
                    numbervalue DECIMAL(18,6),
                    textvalue TEXT,
                    datevalue TIMESTAMP,
                    valuetype VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_customfieldvalue")

            # dim_cwd_directory
            cursor.execute("""
                CREATE TABLE load_test.dim_cwd_directory (
                    dim_cwd_directory_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    directory_name VARCHAR(255),
                    lower_directory_name VARCHAR(255),
                    created_date TIMESTAMP,
                    updated_date TIMESTAMP,
                    active BOOLEAN,
                    description TEXT,
                    impl_class VARCHAR(500),
                    lower_impl_class VARCHAR(500),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_cwd_directory")

            conn.commit()
            logger.info("🎯 First batch of missing tables created")
            
        except Exception as e:
            logger.error(f"❌ Error creating tables: {e}")
            conn.rollback()
        finally:
            conn.close()

    def create_remaining_tables(self):
        """Create the remaining missing dimension tables"""
        logger.info("➕ CREATING REMAINING MISSING DIMENSION TABLES")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            # dim_issuelinktype
            cursor.execute("""
                CREATE TABLE load_test.dim_issuelinktype (
                    dim_issuelinktype_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    linkname VARCHAR(255),
                    inward VARCHAR(255),
                    outward VARCHAR(255),
                    pstyle VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_issuelinktype")

            # dim_label
            cursor.execute("""
                CREATE TABLE load_test.dim_label (
                    dim_label_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    fieldid VARCHAR(255),
                    issue VARCHAR(255),
                    label VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_label")

            # dim_os_currentstep
            cursor.execute("""
                CREATE TABLE load_test.dim_os_currentstep (
                    dim_os_currentstep_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    entry_id VARCHAR(255),
                    step_id VARCHAR(255),
                    action_id VARCHAR(255),
                    owner VARCHAR(255),
                    start_date TIMESTAMP,
                    due_date TIMESTAMP,
                    finish_date TIMESTAMP,
                    status VARCHAR(255),
                    caller VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_os_currentstep")

            # dim_os_wfentry
            cursor.execute("""
                CREATE TABLE load_test.dim_os_wfentry (
                    dim_os_wfentry_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    name VARCHAR(255),
                    initialized INTEGER,
                    state INTEGER,
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_os_wfentry")

            # dim_projectcategory
            cursor.execute("""
                CREATE TABLE load_test.dim_projectcategory (
                    dim_projectcategory_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    cname VARCHAR(255),
                    description TEXT,
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_projectcategory")

            # dim_worklogs
            cursor.execute("""
                CREATE TABLE load_test.dim_worklogs (
                    dim_worklogs_id SERIAL PRIMARY KEY,
                    id VARCHAR(255),
                    issueid VARCHAR(255),
                    author VARCHAR(255),
                    grouplevel VARCHAR(255),
                    rolelevel VARCHAR(255),
                    worklogbody TEXT,
                    created TIMESTAMP,
                    updateauthor VARCHAR(255),
                    updated TIMESTAMP,
                    startdate TIMESTAMP,
                    timeworked BIGINT,
                    author_username VARCHAR(255),
                    author_email VARCHAR(255),
                    author_display_name VARCHAR(255),
                    instance_id INTEGER NOT NULL,
                    loaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("✅ Created dim_worklogs")

            conn.commit()
            logger.info("🎯 All missing tables created successfully")
            
        except Exception as e:
            logger.error(f"❌ Error creating remaining tables: {e}")
            conn.rollback()
        finally:
            conn.close()

    def verify_table_count(self):
        """Verify we now have 40 tables total"""
        logger.info("🔍 VERIFYING TABLE COUNT")
        
        conn = self.connect_dw()
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'load_test'")
            count = cursor.fetchone()[0]
            
            logger.info(f"📊 Total tables in load_test schema: {count}")
            
            if count == 40:
                logger.info("🎉 SUCCESS: Now have 40 tables matching AYA production!")
                return True
            else:
                logger.warning(f"⚠️ Expected 40 tables, got {count}")
                return False
                
        finally:
            conn.close()

    def run_complete_fix(self):
        """Run all fixes to match AYA production database"""
        logger.info("🚀 STARTING COMPLETE FIX TO MATCH AYA PRODUCTION")
        logger.info("=" * 60)
        
        start_time = datetime.now()
        
        try:
            # Step 1: Rename existing tables
            self.rename_existing_tables()
            
            # Step 2: Create missing tables (first batch)
            self.create_missing_tables()
            
            # Step 3: Create remaining tables
            self.create_remaining_tables()
            
            # Step 4: Verify table count
            success = self.verify_table_count()
            
            duration = (datetime.now() - start_time).total_seconds()
            
            logger.info("=" * 60)
            logger.info("COMPLETE FIX FINISHED!")
            logger.info(f"Duration: {duration:.2f} seconds")
            
            if success:
                logger.info("🎉 SUCCESS: Test database now matches AYA production!")
                logger.info("✅ Ready for 40/40 table loading test")
                return True
            else:
                logger.error("❌ FAILED: Table count mismatch")
                return False
                
        except Exception as e:
            logger.error(f"❌ Fatal error: {e}")
            return False

def main():
    """Main entry point"""
    print("🔧 AYA TABLE MATCHER - FIXING TEST DATABASE")
    print("=" * 60)
    print("OBJECTIVE: Add 15 missing tables + rename 5 tables")
    print("TARGET: Match AYA production exactly (40 tables)")
    print("=" * 60)
    
    matcher = AYATableMatcher()
    
    try:
        success = matcher.run_complete_fix()
        
        if success:
            print("🎉 SUCCESS: Test database now matches AYA production!")
            print("✅ Ready to test 40/40 table loading")
            return 0
        else:
            print("❌ FAILED: Could not match AYA production")
            return 1
            
    except Exception as e:
        logger.error(f"❌ Fatal error: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
